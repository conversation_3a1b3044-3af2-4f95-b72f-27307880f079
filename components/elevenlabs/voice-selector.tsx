'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Search, 
  Filter, 
  Play, 
  Pause, 
  Heart, 
  Star, 
  User, 
  Globe, 
  Volume2,
  RefreshCw,
  X
} from 'lucide-react';
import { useElevenLabs } from '@/hooks/use-elevenlabs';
import { useVoiceManagement } from '@/hooks/use-elevenlabs';
import { getVoiceGender, getVoiceAge, getVoiceAccent, getVoiceCategoryLabel } from '@/lib/elevenlabs/utils';
import type { ElevenLabsVoice } from '@/lib/elevenlabs/types';

interface VoiceSelectorProps {
  onVoiceSelect?: (voice: ElevenLabsVoice) => void;
  selectedVoice?: ElevenLabsVoice | null;
  className?: string;
  compact?: boolean;
}

export function VoiceSelector({ 
  onVoiceSelect, 
  selectedVoice, 
  className = '',
  compact = false 
}: VoiceSelectorProps) {
  const { voices, selectVoice, loadVoices, isConfigured } = useElevenLabs();
  const { 
    library, 
    favorites, 
    preview,
    search,
    filter,
    sort,
    clearFilters,
    toggleFavorite,
    startPreview,
    stopPreview
  } = useVoiceManagement();

  const [showFilters, setShowFilters] = useState(false);
  // const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  useEffect(() => {
    if (isConfigured && voices.length === 0) {
      loadVoices();
    }
  }, [isConfigured, voices.length, loadVoices]);

  const handleVoiceSelect = (voice: ElevenLabsVoice) => {
    selectVoice(voice);
    onVoiceSelect?.(voice);
  };

  const handlePreview = (voice: ElevenLabsVoice) => {
    if (preview.isPlaying && preview.currentVoice?.voice_id === voice.voice_id) {
      stopPreview();
    } else {
      startPreview(voice);
    }
  };

  const filteredVoices = library.filteredVoices.length > 0 ? library.filteredVoices : voices;

  if (!isConfigured) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Voice Selector</CardTitle>
          <CardDescription>
            Configure your ElevenLabs API key to access voices.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  if (compact) {
    return (
      <div className={`space-y-2 ${className}`}>
        <Label>Voice</Label>
        <Select
          value={selectedVoice?.voice_id || ''}
          onValueChange={(value) => {
            const voice = voices.find(v => v.voice_id === value);
            if (voice) handleVoiceSelect(voice);
          }}
        >
          <SelectTrigger>
            <SelectValue placeholder="Select a voice">
              {selectedVoice && (
                <div className="flex items-center gap-2">
                  <span>{selectedVoice.name}</span>
                  <Badge variant="secondary" className="text-xs">
                    {getVoiceCategoryLabel(selectedVoice.category)}
                  </Badge>
                </div>
              )}
            </SelectValue>
          </SelectTrigger>
          <SelectContent>
            {filteredVoices.map((voice) => (
              <SelectItem key={voice.voice_id} value={voice.voice_id}>
                <div className="flex items-center gap-2">
                  <span>{voice.name}</span>
                  <Badge variant="secondary" className="text-xs">
                    {getVoiceCategoryLabel(voice.category)}
                  </Badge>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Voice Selector</CardTitle>
            <CardDescription>
              Choose from {filteredVoices.length} available voices
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4" />
              {showFilters ? 'Hide' : 'Show'} Filters
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={loadVoices}
              disabled={library.isLoading}
            >
              <RefreshCw className={`h-4 w-4 ${library.isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search voices..."
            value={library.filters.search}
            onChange={(e) => search(e.target.value)}
            className="pl-9"
          />
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3 p-4 bg-muted rounded-lg">
            <div>
              <Label className="text-xs">Category</Label>
              <Select
                value={library.filters.category}
                onValueChange={(value) => filter({ category: value })}
              >
                <SelectTrigger className="h-8">
                  <SelectValue placeholder="Any" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Any</SelectItem>
                  <SelectItem value="premade">Premade</SelectItem>
                  <SelectItem value="cloned">Cloned</SelectItem>
                  <SelectItem value="generated">Generated</SelectItem>
                  <SelectItem value="professional">Professional</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className="text-xs">Gender</Label>
              <Select
                value={library.filters.gender}
                onValueChange={(value) => filter({ gender: value })}
              >
                <SelectTrigger className="h-8">
                  <SelectValue placeholder="Any" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Any</SelectItem>
                  <SelectItem value="male">Male</SelectItem>
                  <SelectItem value="female">Female</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className="text-xs">Age</Label>
              <Select
                value={library.filters.age}
                onValueChange={(value) => filter({ age: value })}
              >
                <SelectTrigger className="h-8">
                  <SelectValue placeholder="Any" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Any</SelectItem>
                  <SelectItem value="young">Young</SelectItem>
                  <SelectItem value="middle_aged">Middle Aged</SelectItem>
                  <SelectItem value="old">Old</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label className="text-xs">Accent</Label>
              <Select
                value={library.filters.accent}
                onValueChange={(value) => filter({ accent: value })}
              >
                <SelectTrigger className="h-8">
                  <SelectValue placeholder="Any" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Any</SelectItem>
                  <SelectItem value="american">American</SelectItem>
                  <SelectItem value="british">British</SelectItem>
                  <SelectItem value="australian">Australian</SelectItem>
                  <SelectItem value="indian">Indian</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={clearFilters}
                className="h-8"
              >
                <X className="h-4 w-4" />
                Clear
              </Button>
            </div>
          </div>
        )}

        {/* Sort */}
        <div className="flex items-center gap-2">
          <Label className="text-sm">Sort by:</Label>
          <Select
            value={library.sort.field}
            onValueChange={(value: any) => sort(value)}
          >
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="name">Name</SelectItem>
              <SelectItem value="category">Category</SelectItem>
              <SelectItem value="date_created">Date Created</SelectItem>
            </SelectContent>
          </Select>
          <Button
            variant="outline"
            size="sm"
            onClick={() => sort(library.sort.field, library.sort.direction === 'asc' ? 'desc' : 'asc')}
          >
            {library.sort.direction === 'asc' ? '↑' : '↓'}
          </Button>
        </div>

        <Separator />

        {/* Voice List */}
        <ScrollArea className="h-[400px]">
          {library.isLoading ? (
            <div className="flex items-center justify-center h-32">
              <RefreshCw className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading voices...</span>
            </div>
          ) : filteredVoices.length === 0 ? (
            <div className="text-center text-muted-foreground py-8">
              No voices found. Try adjusting your filters.
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
              {filteredVoices.map((voice) => (
                <Card 
                  key={voice.voice_id} 
                  className={`cursor-pointer transition-colors hover:bg-accent ${
                    selectedVoice?.voice_id === voice.voice_id ? 'ring-2 ring-primary' : ''
                  }`}
                  onClick={() => handleVoiceSelect(voice)}
                >
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-sm">{voice.name}</CardTitle>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleFavorite(voice.voice_id);
                          }}
                          className="h-6 w-6 p-0"
                        >
                          <Heart 
                            className={`h-3 w-3 ${
                              favorites.voiceIds.includes(voice.voice_id) 
                                ? 'fill-current text-red-500' 
                                : ''
                            }`} 
                          />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handlePreview(voice);
                          }}
                          className="h-6 w-6 p-0"
                        >
                          {preview.isPlaying && preview.currentVoice?.voice_id === voice.voice_id ? (
                            <Pause className="h-3 w-3" />
                          ) : (
                            <Play className="h-3 w-3" />
                          )}
                        </Button>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent className="pt-0">
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-xs">
                        <Badge variant="secondary">
                          {getVoiceCategoryLabel(voice.category)}
                        </Badge>
                        {voice.category === 'professional' && (
                          <Star className="h-3 w-3 text-yellow-500" />
                        )}
                      </div>
                      <div className="flex items-center gap-4 text-xs text-muted-foreground">
                        <div className="flex items-center gap-1">
                          <User className="h-3 w-3" />
                          {getVoiceGender(voice)}
                        </div>
                        <div className="flex items-center gap-1">
                          <Volume2 className="h-3 w-3" />
                          {getVoiceAge(voice)}
                        </div>
                        <div className="flex items-center gap-1">
                          <Globe className="h-3 w-3" />
                          {getVoiceAccent(voice)}
                        </div>
                      </div>
                      {voice.description && (
                        <p className="text-xs text-muted-foreground line-clamp-2">
                          {voice.description}
                        </p>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </ScrollArea>
      </CardContent>
    </Card>
  );
}