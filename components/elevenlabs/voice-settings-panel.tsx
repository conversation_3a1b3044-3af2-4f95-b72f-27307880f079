'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Slider } from '@/components/ui/slider';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Volume2, 
  Settings, 
  Play,
  RotateCcw,
  Save,
  Mic,
  Headphones,
  AudioWaveform,
  Sparkles
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { VoiceSelector } from './voice-selector';
import { VOICE_SETTINGS_DEFAULTS } from '@/lib/elevenlabs/constants';
import type { ElevenLabsVoice } from '@/lib/elevenlabs/types';

interface VoiceSettingsPanelProps {
  voices: ElevenLabsVoice[];
  selectedVoice: ElevenLabsVoice | null;
  onVoiceSelect: (voice: ElevenLabsVoice) => void;
  className?: string;
}

export function VoiceSettingsPanel({
  voices,
  selectedVoice,
  onVoiceSelect,
  className
}: VoiceSettingsPanelProps) {
  const [voiceSettings, setVoiceSettings] = useState({
    stability: VOICE_SETTINGS_DEFAULTS.STABILITY,
    similarity_boost: VOICE_SETTINGS_DEFAULTS.SIMILARITY_BOOST,
    style: VOICE_SETTINGS_DEFAULTS.STYLE,
    use_speaker_boost: VOICE_SETTINGS_DEFAULTS.USE_SPEAKER_BOOST,
  });

  const [isPreviewMode, setIsPreviewMode] = useState(false);

  const handleSettingChange = (key: string, value: number | boolean) => {
    setVoiceSettings(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleResetSettings = () => {
    setVoiceSettings({
      stability: VOICE_SETTINGS_DEFAULTS.STABILITY,
      similarity_boost: VOICE_SETTINGS_DEFAULTS.SIMILARITY_BOOST,
      style: VOICE_SETTINGS_DEFAULTS.STYLE,
      use_speaker_boost: VOICE_SETTINGS_DEFAULTS.USE_SPEAKER_BOOST,
    });
  };

  const handleSavePreset = () => {
    // Save current settings as preset
    console.log('Saving preset:', voiceSettings);
  };

  const handlePreviewVoice = () => {
    setIsPreviewMode(!isPreviewMode);
    // Trigger voice preview
  };

  return (
    <div className={cn("h-full flex flex-col", className)}>
      <Card className="flex-1 flex flex-col">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm flex items-center gap-2">
            <Volume2 className="h-4 w-4" />
            Voice Settings
          </CardTitle>
        </CardHeader>
        
        <CardContent className="flex-1 flex flex-col gap-4">
          <Tabs defaultValue="selection" className="flex-1 flex flex-col">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="selection" className="text-xs">Selection</TabsTrigger>
              <TabsTrigger value="settings" className="text-xs">Settings</TabsTrigger>
              <TabsTrigger value="presets" className="text-xs">Presets</TabsTrigger>
            </TabsList>

            <TabsContent value="selection" className="flex-1 flex flex-col gap-4">
              {/* Voice Selection */}
              <div className="space-y-3">
                <Label className="text-xs">Choose Voice</Label>
                <VoiceSelector
                  selectedVoice={selectedVoice}
                  onVoiceSelect={onVoiceSelect}
                  compact={false}
                />
              </div>

              {/* Voice Info */}
              {selectedVoice && (
                <div className="space-y-3">
                  <Separator />
                  <div className="space-y-2">
                    <Label className="text-xs">Voice Information</Label>
                    <div className="bg-muted/20 rounded-lg p-3 space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-muted-foreground">Name:</span>
                        <span className="text-xs font-medium">{selectedVoice.name}</span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-muted-foreground">Category:</span>
                        <Badge variant="outline" className="text-xs">
                          {selectedVoice.category}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-muted-foreground">Gender:</span>
                        <span className="text-xs">
                          {selectedVoice.labels?.gender || 'Unknown'}
                        </span>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-muted-foreground">Age:</span>
                        <span className="text-xs">
                          {selectedVoice.labels?.age || 'Unknown'}
                        </span>
                      </div>
                    </div>
                  </div>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handlePreviewVoice}
                    className="w-full"
                  >
                    <Play className="h-4 w-4 mr-2" />
                    Preview Voice
                  </Button>
                </div>
              )}
            </TabsContent>

            <TabsContent value="settings" className="flex-1 flex flex-col gap-4">
              {/* Voice Settings */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label className="text-xs">Voice Parameters</Label>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleResetSettings}
                    className="h-6 text-xs"
                  >
                    <RotateCcw className="h-3 w-3 mr-1" />
                    Reset
                  </Button>
                </div>

                {/* Stability */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">Stability</Label>
                    <span className="text-xs text-muted-foreground">
                      {voiceSettings.stability.toFixed(2)}
                    </span>
                  </div>
                  <Slider
                    value={[voiceSettings.stability]}
                    onValueChange={(value) => handleSettingChange('stability', value[0])}
                    max={1}
                    step={0.01}
                    className="w-full"
                  />
                  <p className="text-xs text-muted-foreground">
                    Controls voice consistency. Higher values = more stable, lower = more variable.
                  </p>
                </div>

                {/* Similarity Boost */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">Similarity Boost</Label>
                    <span className="text-xs text-muted-foreground">
                      {voiceSettings.similarity_boost.toFixed(2)}
                    </span>
                  </div>
                  <Slider
                    value={[voiceSettings.similarity_boost]}
                    onValueChange={(value) => handleSettingChange('similarity_boost', value[0])}
                    max={1}
                    step={0.01}
                    className="w-full"
                  />
                  <p className="text-xs text-muted-foreground">
                    Enhances similarity to original voice. Higher values = more similar.
                  </p>
                </div>

                {/* Style */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">Style</Label>
                    <span className="text-xs text-muted-foreground">
                      {voiceSettings.style.toFixed(2)}
                    </span>
                  </div>
                  <Slider
                    value={[voiceSettings.style]}
                    onValueChange={(value) => handleSettingChange('style', value[0])}
                    max={1}
                    step={0.01}
                    className="w-full"
                  />
                  <p className="text-xs text-muted-foreground">
                    Adjusts speaking style. Higher values = more expressive.
                  </p>
                </div>

                {/* Speaker Boost */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label className="text-xs">Speaker Boost</Label>
                    <Switch
                      checked={voiceSettings.use_speaker_boost}
                      onCheckedChange={(checked) => handleSettingChange('use_speaker_boost', checked)}
                    />
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Enhances voice clarity and reduces background noise.
                  </p>
                </div>
              </div>

              <Separator />

              {/* Quick Actions */}
              <div className="space-y-2">
                <Label className="text-xs">Quick Actions</Label>
                <div className="grid grid-cols-2 gap-2">
                  <Button variant="outline" size="sm" onClick={handleSavePreset}>
                    <Save className="h-3 w-3 mr-1" />
                    Save Preset
                  </Button>
                  <Button variant="outline" size="sm" onClick={handlePreviewVoice}>
                    <Headphones className="h-3 w-3 mr-1" />
                    Test Voice
                  </Button>
                </div>
              </div>
            </TabsContent>

            <TabsContent value="presets" className="flex-1 flex flex-col gap-4">
              {/* Preset Categories */}
              <div className="space-y-3">
                <Label className="text-xs">Preset Categories</Label>
                
                {/* Built-in Presets */}
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground">Built-in</Label>
                  <div className="grid grid-cols-2 gap-2">
                    <Button variant="outline" size="sm" className="text-xs">
                      <Mic className="h-3 w-3 mr-1" />
                      Podcast
                    </Button>
                    <Button variant="outline" size="sm" className="text-xs">
                      <AudioWaveform className="h-3 w-3 mr-1" />
                      Audiobook
                    </Button>
                    <Button variant="outline" size="sm" className="text-xs">
                      <Sparkles className="h-3 w-3 mr-1" />
                      Commercial
                    </Button>
                    <Button variant="outline" size="sm" className="text-xs">
                      <Settings className="h-3 w-3 mr-1" />
                      Tutorial
                    </Button>
                  </div>
                </div>

                {/* Custom Presets */}
                <div className="space-y-2">
                  <Label className="text-xs text-muted-foreground">Custom</Label>
                  <div className="bg-muted/20 rounded-lg p-3 text-center">
                    <p className="text-xs text-muted-foreground">
                      No custom presets yet. Create your first preset by adjusting settings and saving.
                    </p>
                  </div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}