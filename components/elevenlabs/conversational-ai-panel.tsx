'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { 
  Bot, 
  PhoneCall, 
  PhoneOff, 
  Mic, 
  MicOff, 
  Send, 
  Settings,
  MessageCircle,
  Activity,
  Zap,
  User
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatDurationMs } from '@/lib/elevenlabs/utils';
import type { ConversationalAIAgent, ConversationalMessage } from '@/lib/elevenlabs/types';

interface ConversationalAIPanelProps {
  agents: ConversationalAIAgent[];
  selectedAgent: ConversationalAIAgent | null;
  isConnected: boolean;
  onConnect: (agentId: string) => void;
  onDisconnect: () => void;
  onSendMessage: (message: string) => void;
  className?: string;
}

export function ConversationalAIPanel({
  agents,
  selectedAgent,
  isConnected,
  onConnect,
  onDisconnect,
  onSendMessage,
  className
}: ConversationalAIPanelProps) {
  const [messages, setMessages] = useState<ConversationalMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const [isRecording, setIsRecording] = useState(false);
  const [sessionMetrics, setSessionMetrics] = useState({
    duration: 0,
    messagesCount: 0,
    averageResponseTime: 0
  });
  
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const handleConnect = () => {
    if (selectedAgent) {
      onConnect(selectedAgent.agent_id);
    }
  };

  const handleDisconnect = () => {
    onDisconnect();
    setMessages([]);
    setSessionMetrics({
      duration: 0,
      messagesCount: 0,
      averageResponseTime: 0
    });
  };

  const handleSendMessage = () => {
    if (!inputMessage.trim()) return;
    
    const userMessage: ConversationalMessage = {
      id: `msg_${Date.now()}`,
      type: 'text',
      content: inputMessage,
      timestamp: Date.now(),
      sender: 'user',
      metadata: {}
    };
    
    setMessages(prev => [...prev, userMessage]);
    onSendMessage(inputMessage);
    setInputMessage('');
    inputRef.current?.focus();
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const toggleRecording = () => {
    setIsRecording(!isRecording);
    // Handle voice recording logic here
  };

  const renderMessage = (message: ConversationalMessage) => {
    const isUser = message.sender === 'user';
    
    return (
      <div
        key={message.id}
        className={cn(
          "flex gap-3 p-3 rounded-lg",
          isUser ? "bg-primary/10 ml-8" : "bg-muted/30 mr-8"
        )}
      >
        <Avatar className="h-8 w-8 shrink-0">
          <AvatarFallback>
            {isUser ? <User className="h-4 w-4" /> : <Bot className="h-4 w-4" />}
          </AvatarFallback>
        </Avatar>
        
        <div className="flex-1 space-y-1">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">
              {isUser ? 'You' : selectedAgent?.name || 'Assistant'}
            </span>
            <span className="text-xs text-muted-foreground">
              {new Date(message.timestamp).toLocaleTimeString()}
            </span>
          </div>
          
          <div className="text-sm">{message.content}</div>
          
          {message.metadata?.responseTime && (
            <div className="text-xs text-muted-foreground">
              Response time: {message.metadata.responseTime}ms
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    <div className={cn("h-full flex flex-col", className)}>
      <Card className="flex-1 flex flex-col">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center gap-2">
              <Bot className="h-4 w-4" />
              Conversational AI
            </CardTitle>
            <div className="flex items-center gap-2">
              {isConnected && (
                <Badge variant="secondary" className="text-xs">
                  <Activity className="h-3 w-3 mr-1" />
                  Connected
                </Badge>
              )}
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                <Settings className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="flex-1 flex flex-col gap-4">
          {/* Agent Selection */}
          <div className="space-y-2">
            <Label className="text-xs">Select Agent</Label>
            <div className="flex items-center gap-2">
              <select 
                className="flex-1 h-8 px-3 text-sm border border-input rounded-md bg-background"
                disabled={isConnected}
              >
                <option value="">Choose an agent...</option>
                {agents.map(agent => (
                  <option key={agent.agent_id} value={agent.agent_id}>
                    {agent.name}
                  </option>
                ))}
              </select>
              <Button
                variant={isConnected ? "destructive" : "default"}
                size="sm"
                onClick={isConnected ? handleDisconnect : handleConnect}
                disabled={!selectedAgent}
              >
                {isConnected ? (
                  <>
                    <PhoneOff className="h-4 w-4 mr-1" />
                    Disconnect
                  </>
                ) : (
                  <>
                    <PhoneCall className="h-4 w-4 mr-1" />
                    Connect
                  </>
                )}
              </Button>
            </div>
          </div>

          {/* Session Metrics */}
          {isConnected && (
            <div className="flex items-center gap-4 p-2 bg-muted/20 rounded-lg">
              <div className="flex items-center gap-1">
                <Zap className="h-3 w-3 text-blue-500" />
                <span className="text-xs">
                  {formatDurationMs(sessionMetrics.duration)}
                </span>
              </div>
              <div className="flex items-center gap-1">
                <MessageCircle className="h-3 w-3 text-green-500" />
                <span className="text-xs">
                  {sessionMetrics.messagesCount} messages
                </span>
              </div>
              <div className="flex items-center gap-1">
                <Activity className="h-3 w-3 text-orange-500" />
                <span className="text-xs">
                  {sessionMetrics.averageResponseTime}ms avg
                </span>
              </div>
            </div>
          )}

          {/* Chat Area */}
          <div className="flex-1 flex flex-col gap-2">
            {isConnected ? (
              <>
                {/* Messages */}
                <ScrollArea className="flex-1 pr-4">
                  <div className="space-y-3">
                    {messages.length === 0 ? (
                      <div className="text-center text-muted-foreground py-8">
                        <Bot className="h-8 w-8 mx-auto mb-2" />
                        <p className="text-sm">Start a conversation with {selectedAgent?.name}</p>
                      </div>
                    ) : (
                      messages.map(renderMessage)
                    )}
                    <div ref={messagesEndRef} />
                  </div>
                </ScrollArea>

                <Separator />

                {/* Input Area */}
                <div className="space-y-2">
                  <div className="flex gap-2">
                    <Input
                      ref={inputRef}
                      value={inputMessage}
                      onChange={(e) => setInputMessage(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Type your message..."
                      className="flex-1"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={toggleRecording}
                      className={cn(
                        "h-10 w-10 p-0",
                        isRecording && "bg-red-500 text-white hover:bg-red-600"
                      )}
                    >
                      {isRecording ? (
                        <MicOff className="h-4 w-4" />
                      ) : (
                        <Mic className="h-4 w-4" />
                      )}
                    </Button>
                    <Button
                      onClick={handleSendMessage}
                      disabled={!inputMessage.trim()}
                      size="sm"
                      className="h-10 w-10 p-0"
                    >
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                  
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <span>Press Enter to send, Shift+Enter for new line</span>
                    {isRecording && (
                      <Badge variant="destructive" className="text-xs">
                        Recording...
                      </Badge>
                    )}
                  </div>
                </div>
              </>
            ) : (
              <div className="flex-1 flex items-center justify-center">
                <div className="text-center text-muted-foreground">
                  <Bot className="h-12 w-12 mx-auto mb-4" />
                  <h3 className="font-medium mb-2">Connect to Start Chatting</h3>
                  <p className="text-sm">
                    Select an agent and click connect to begin your conversation
                  </p>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}