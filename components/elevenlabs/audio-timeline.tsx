'use client';

import React, { useRef, useEffect, useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { 
  Play, 
  Pause, 
  SkipBack, 
  SkipForward,
  Volume2,
  AudioWaveform,
  Scissors,
  Copy,
  Trash2,
  Plus,
  ZoomIn,
  ZoomOut,
  Maximize2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatDuration } from '@/lib/elevenlabs/utils';
import type { AudioClip } from './elevenlabs-studio-layout';

interface AudioTimelineProps {
  clips: AudioClip[];
  currentTime: number;
  duration: number;
  isPlaying: boolean;
  onTimeChange: (time: number) => void;
  onPlayPause: () => void;
  onClipSelect: (clip: AudioClip) => void;
  onClipDelete: (clipId: string) => void;
  onClipDuplicate: (clip: AudioClip) => void;
  className?: string;
}

export function AudioTimeline({
  clips,
  currentTime,
  duration,
  isPlaying,
  onTimeChange,
  onPlayPause,
  onClipSelect,
  onClipDelete,
  onClipDuplicate,
  className
}: AudioTimelineProps) {
  const [zoom, setZoom] = useState(1);
  const [selectedClipId, setSelectedClipId] = useState<string | null>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const timelineRef = useRef<HTMLDivElement>(null);

  const handleTimelineClick = (e: React.MouseEvent) => {
    if (!timelineRef.current) return;
    
    const rect = timelineRef.current.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const percentage = x / rect.width;
    const newTime = percentage * duration;
    
    onTimeChange(Math.max(0, Math.min(duration, newTime)));
  };

  const handleClipClick = (clip: AudioClip) => {
    setSelectedClipId(clip.id);
    onClipSelect(clip);
  };

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev * 1.5, 10));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev / 1.5, 0.1));
  };

  const renderTimeMarkers = () => {
    const markers = [];
    const interval = duration / 10; // 10 markers
    
    for (let i = 0; i <= 10; i++) {
      const time = i * interval;
      const position = (time / duration) * 100;
      
      markers.push(
        <div
          key={i}
          className="absolute top-0 bottom-0 border-l border-muted-foreground/20"
          style={{ left: `${position}%` }}
        >
          <span className="absolute top-0 left-1 text-xs text-muted-foreground">
            {formatDuration(time)}
          </span>
        </div>
      );
    }
    
    return markers;
  };

  const renderClips = () => {
    return clips.map((clip) => {
      const startPosition = (clip.position / duration) * 100;
      const width = (clip.duration / duration) * 100;
      const isSelected = selectedClipId === clip.id;
      
      return (
        <div
          key={clip.id}
          className={cn(
            "absolute h-12 bg-primary/20 border rounded cursor-pointer transition-all",
            isSelected && "ring-2 ring-primary bg-primary/30"
          )}
          style={{
            left: `${startPosition}%`,
            width: `${width}%`,
            top: '32px'
          }}
          onClick={() => handleClipClick(clip)}
        >
          <div className="h-full flex items-center justify-between px-2">
            <div className="flex items-center gap-1">
              <AudioWaveform className="h-3 w-3" />
              <span className="text-xs truncate">{clip.voice.name}</span>
            </div>
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onClipDuplicate(clip);
                }}
                className="h-4 w-4 p-0"
              >
                <Copy className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onClipDelete(clip.id);
                }}
                className="h-4 w-4 p-0"
              >
                <Trash2 className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </div>
      );
    });
  };

  return (
    <div className={cn("h-full flex flex-col", className)}>
      <Card className="flex-1 flex flex-col">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center gap-2">
              <AudioWaveform className="h-4 w-4" />
              Audio Timeline
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleZoomOut}
                className="h-6 w-6 p-0"
              >
                <ZoomOut className="h-3 w-3" />
              </Button>
              <span className="text-xs text-muted-foreground">
                {Math.round(zoom * 100)}%
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={handleZoomIn}
                className="h-6 w-6 p-0"
              >
                <ZoomIn className="h-3 w-3" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsFullscreen(!isFullscreen)}
                className="h-6 w-6 p-0"
              >
                <Maximize2 className="h-3 w-3" />
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="flex-1 flex flex-col gap-4">
          {/* Playback Controls */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={onPlayPause}
                className="h-8 w-8 p-0"
              >
                {isPlaying ? (
                  <Pause className="h-4 w-4" />
                ) : (
                  <Play className="h-4 w-4" />
                )}
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="h-8 w-8 p-0"
              >
                <SkipBack className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                className="h-8 w-8 p-0"
              >
                <SkipForward className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="flex items-center gap-2">
              <span className="text-xs text-muted-foreground">
                {formatDuration(currentTime)} / {formatDuration(duration)}
              </span>
              <Badge variant="outline" className="text-xs">
                {clips.length} clips
              </Badge>
            </div>
          </div>

          {/* Timeline */}
          <div className="flex-1 flex flex-col gap-2">
            {/* Time ruler */}
            <div className="h-6 relative bg-muted/20 rounded">
              {renderTimeMarkers()}
            </div>

            {/* Timeline tracks */}
            <div
              ref={timelineRef}
              className="flex-1 relative bg-muted/10 rounded-lg cursor-pointer min-h-20"
              onClick={handleTimelineClick}
              style={{ transform: `scaleX(${zoom})`, transformOrigin: 'left' }}
            >
              {/* Playhead */}
              <div
                className="absolute top-0 bottom-0 w-0.5 bg-primary z-10"
                style={{ left: `${(currentTime / duration) * 100}%` }}
              >
                <div className="absolute -top-1 -left-1 w-2 h-2 bg-primary rounded-full" />
              </div>

              {/* Clips */}
              {renderClips()}
              
              {/* Drop zone indicators */}
              {clips.length === 0 && (
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center text-muted-foreground">
                    <AudioWaveform className="h-8 w-8 mx-auto mb-2" />
                    <p className="text-sm">Drag audio clips here or generate new ones</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Timeline Tools */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Plus className="h-4 w-4 mr-1" />
                Add Clip
              </Button>
              <Button variant="outline" size="sm">
                <Scissors className="h-4 w-4 mr-1" />
                Split
              </Button>
            </div>
            
            <div className="flex items-center gap-2">
              <Volume2 className="h-4 w-4" />
              <Slider
                value={[1]}
                onValueChange={() => {}}
                max={1}
                step={0.1}
                className="w-20"
              />
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}