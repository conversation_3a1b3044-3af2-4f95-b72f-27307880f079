'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { X, Plus } from 'lucide-react';
import { cn } from '@/lib/utils';

export interface TabItem {
  id: string;
  title: string;
  modified?: boolean;
  icon?: React.ReactNode;
}

interface VSCodeTabsProps {
  tabs: TabItem[];
  activeTabId: string;
  onTabChange: (tabId: string) => void;
  onTabClose: (tabId: string) => void;
  onNewTab: () => void;
  className?: string;
}

export function VSCodeTabs({
  tabs,
  activeTabId,
  onTabChange,
  onTabClose,
  onNewTab,
  className
}: VSCodeTabsProps) {
  return (
    <div className={cn("flex items-center bg-muted/20 border-b border-border", className)}>
      {/* Tab List */}
      <div className="flex-1 flex items-center overflow-x-auto">
        {tabs.map((tab) => (
          <div
            key={tab.id}
            className={cn(
              "flex items-center gap-2 px-3 py-2 border-r border-border/50 cursor-pointer transition-colors group min-w-0",
              activeTabId === tab.id
                ? "bg-background border-b-2 border-b-primary"
                : "hover:bg-muted/50"
            )}
            onClick={() => onTabChange(tab.id)}
          >
            {tab.icon && (
              <div className="shrink-0">
                {tab.icon}
              </div>
            )}
            <span className="text-sm truncate min-w-0">
              {tab.title}
              {tab.modified && (
                <span className="ml-1 text-muted-foreground">•</span>
              )}
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                onTabClose(tab.id);
              }}
              className="h-4 w-4 p-0 opacity-0 group-hover:opacity-100 transition-opacity shrink-0"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        ))}
        
        {/* New Tab Button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={onNewTab}
          className="h-8 w-8 p-0 shrink-0"
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}

interface TabContentProps {
  activeTabId: string;
  children: React.ReactNode;
  className?: string;
}

export function TabContent({ activeTabId, children, className }: TabContentProps) {
  return (
    <div className={cn("flex-1 overflow-hidden", className)}>
      {children}
    </div>
  );
}

interface TabPanelProps {
  tabId: string;
  activeTabId: string;
  children: React.ReactNode;
  className?: string;
}

export function TabPanel({ tabId, activeTabId, children, className }: TabPanelProps) {
  if (tabId !== activeTabId) return null;
  
  return (
    <div className={cn("h-full", className)}>
      {children}
    </div>
  );
}

export const DEFAULT_TABS: TabItem[] = [
  { id: 'text-editor', title: 'Text Editor', modified: false },
  { id: 'voice-settings', title: 'Voice Settings', modified: false },
  { id: 'conversational-ai', title: 'Conversational AI', modified: false },
  { id: 'audio-library', title: 'Audio Library', modified: false },
];