'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Type, 
  Volume2, 
  Zap, 
  MoreVertical,
  FileText,
  Copy,
  Clipboard,
  RotateCcw,
  Loader2,
  Play,
  Pause,
  Settings,
  Sparkles
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import { VoiceSelector } from './voice-selector';
import { cleanTextForTTS } from '@/lib/elevenlabs/utils';
import type { ElevenLabsVoice, GenerationState } from '@/lib/elevenlabs/types';

interface TextEditorPanelProps {
  onGenerate: (text: string, voice: ElevenLabsVoice, settings: any) => void;
  selectedVoice: ElevenLabsVoice | null;
  generation: GenerationState;
  className?: string;
}

const textTemplates = [
  { id: 'welcome', label: 'Welcome Message', text: 'Welcome to our service! We\'re excited to help you create amazing audio content.' },
  { id: 'announcement', label: 'Announcement', text: 'Attention everyone! We have an important announcement to share with you today.' },
  { id: 'tutorial', label: 'Tutorial Intro', text: 'In this tutorial, we\'ll walk you through the step-by-step process of using our platform.' },
  { id: 'podcast', label: 'Podcast Intro', text: 'Welcome back to our podcast! Today we\'re discussing the latest trends in AI and technology.' },
  { id: 'commercial', label: 'Commercial', text: 'Discover the power of AI-generated voices. Transform your content with realistic, natural-sounding speech.' },
];

export function TextEditorPanel({
  onGenerate,
  selectedVoice,
  generation,
  className
}: TextEditorPanelProps) {
  const [text, setText] = useState('');
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [wordCount, setWordCount] = useState(0);
  const [estimatedDuration, setEstimatedDuration] = useState(0);
  const [voiceSettings, setVoiceSettings] = useState({
    stability: 0.5,
    similarity_boost: 0.5,
    style: 0.0,
    use_speaker_boost: true,
  });

  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Update word count and estimated duration
  useEffect(() => {
    const words = text.trim().split(/\s+/).filter(word => word.length > 0);
    setWordCount(words.length);
    
    // Estimate duration: average 150 words per minute
    const estimatedMinutes = words.length / 150;
    setEstimatedDuration(estimatedMinutes * 60); // Convert to seconds
  }, [text]);

  const handleTextChange = (value: string) => {
    setText(value);
  };

  const handleTemplateSelect = (template: typeof textTemplates[0]) => {
    setText(template.text);
    textareaRef.current?.focus();
  };

  const handleGenerate = () => {
    if (!text.trim() || !selectedVoice) return;
    
    const cleanedText = cleanTextForTTS(text);
    onGenerate(cleanedText, selectedVoice, voiceSettings);
  };

  const handleCopyText = () => {
    navigator.clipboard.writeText(text);
  };

  const handlePasteText = async () => {
    try {
      const clipboardText = await navigator.clipboard.readText();
      setText(clipboardText);
    } catch (error) {
      console.error('Failed to paste text:', error);
    }
  };

  const handleClearText = () => {
    setText('');
    textareaRef.current?.focus();
  };

  const canGenerate = Boolean(text.trim() && selectedVoice && !generation.isGenerating);

  return (
    <div className={cn("h-full flex flex-col", className)}>
      <Card className="flex-1 flex flex-col">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center gap-2">
              <Type className="h-4 w-4" />
              Text Editor
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsPreviewMode(!isPreviewMode)}
                className="h-6 text-xs"
              >
                {isPreviewMode ? 'Edit' : 'Preview'}
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <MoreVertical className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handleCopyText}>
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Text
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handlePasteText}>
                    <Clipboard className="h-4 w-4 mr-2" />
                    Paste Text
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleClearText}>
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Clear Text
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="flex-1 flex flex-col gap-4">
          {/* Text Templates */}
          <div className="space-y-2">
            <Label className="text-xs">Quick Templates</Label>
            <div className="flex flex-wrap gap-1">
              {textTemplates.map((template) => (
                <Button
                  key={template.id}
                  variant="outline"
                  size="sm"
                  onClick={() => handleTemplateSelect(template)}
                  className="h-6 text-xs"
                >
                  {template.label}
                </Button>
              ))}
            </div>
          </div>

          <Separator />

          {/* Voice Selection */}
          <div className="space-y-2">
            <Label className="text-xs">Voice</Label>
            <VoiceSelector
              selectedVoice={selectedVoice}
              onVoiceSelect={() => {}} // Handled by parent
              compact={true}
            />
          </div>

          {/* Text Input */}
          <div className="flex-1 flex flex-col gap-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="text-input" className="text-xs">
                Text Content
              </Label>
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <span>{wordCount} words</span>
                <span>•</span>
                <span>~{Math.round(estimatedDuration)}s</span>
              </div>
            </div>
            
            {isPreviewMode ? (
              <div className="flex-1 bg-muted/20 rounded-lg p-4 text-sm leading-relaxed overflow-y-auto">
                {text || (
                  <span className="text-muted-foreground italic">
                    No text to preview. Start typing to see your content here.
                  </span>
                )}
              </div>
            ) : (
              <Textarea
                ref={textareaRef}
                id="text-input"
                value={text}
                onChange={(e) => handleTextChange(e.target.value)}
                placeholder="Enter the text you want to convert to speech..."
                className="flex-1 resize-none text-sm"
                maxLength={5000}
              />
            )}
            
            <div className="flex items-center justify-between text-xs text-muted-foreground">
              <span>{text.length} / 5000 characters</span>
              <div className="flex items-center gap-2">
                {generation.isGenerating && (
                  <Badge variant="secondary" className="text-xs">
                    <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                    {Math.round(generation.progress)}%
                  </Badge>
                )}
                {generation.error && (
                  <Badge variant="destructive" className="text-xs">
                    Error
                  </Badge>
                )}
              </div>
            </div>
          </div>

          <Separator />

          {/* Generation Controls */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Label className="text-xs">Generation Settings</Label>
              <Button variant="ghost" size="sm" className="h-6 text-xs">
                <Settings className="h-3 w-3 mr-1" />
                Advanced
              </Button>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label className="text-xs">Model</Label>
                <Select defaultValue="eleven_multilingual_v2">
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="eleven_multilingual_v2">Multilingual v2</SelectItem>
                    <SelectItem value="eleven_turbo_v2_5">Turbo v2.5</SelectItem>
                    <SelectItem value="eleven_flash_v2_5">Flash v2.5</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-xs">Output Format</Label>
                <Select defaultValue="mp3_44100_128">
                  <SelectTrigger className="h-8 text-xs">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="mp3_44100_128">MP3 128kbps</SelectItem>
                    <SelectItem value="mp3_44100_192">MP3 192kbps</SelectItem>
                    <SelectItem value="pcm_44100">PCM 44.1kHz</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <Button
              onClick={handleGenerate}
              disabled={!canGenerate}
              className="w-full"
              size="sm"
            >
              {generation.isGenerating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Volume2 className="h-4 w-4 mr-2" />
                  Generate Speech
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}