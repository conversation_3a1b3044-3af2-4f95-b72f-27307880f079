'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  Type, 
  Volume2, 
  Bot, 
  AudioWaveform, 
  Settings, 
  History,
  FolderOpen,
  Plus,
  Mic,
  Headphones
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface AudioEditorSidebarProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
  className?: string;
}

const sidebarItems = [
  { id: 'text-editor', icon: Type, label: 'Text Editor', shortcut: 'Ctrl+T' },
  { id: 'voice-settings', icon: Volume2, label: 'Voice Settings', shortcut: 'Ctrl+V' },
  { id: 'conversational-ai', icon: Bot, label: 'Conversational AI', shortcut: 'Ctrl+C' },
  { id: 'audio-library', icon: AudioWaveform, label: 'Audio Library', shortcut: 'Ctrl+L' },
  { id: 'voice-lab', icon: Mic, label: 'Voice Lab', shortcut: 'Ctrl+B' },
  { id: 'projects', icon: FolderOpen, label: 'Projects', shortcut: 'Ctrl+P' },
  { id: 'history', icon: History, label: 'History', shortcut: 'Ctrl+H' },
];

const bottomItems = [
  { id: 'audio-preview', icon: Headphones, label: 'Audio Preview', shortcut: 'Ctrl+Space' },
  { id: 'settings', icon: Settings, label: 'Settings', shortcut: 'Ctrl+,' },
];

export function AudioEditorSidebar({ activeTab, onTabChange, className }: AudioEditorSidebarProps) {
  return (
    <TooltipProvider>
      <div className={cn("h-full bg-muted/30 border-r border-border flex flex-col", className)}>
        {/* Main Navigation */}
        <div className="flex-1 flex flex-col gap-1 p-2">
          {sidebarItems.map((item) => (
            <Tooltip key={item.id}>
              <TooltipTrigger asChild>
                <Button
                  variant={activeTab === item.id ? "secondary" : "ghost"}
                  size="sm"
                  onClick={() => onTabChange(item.id)}
                  className={cn(
                    "w-full h-10 justify-start p-2",
                    activeTab === item.id && "bg-secondary"
                  )}
                >
                  <item.icon className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right" className="flex items-center gap-2">
                <span>{item.label}</span>
                <kbd className="text-xs bg-muted px-1 py-0.5 rounded">
                  {item.shortcut}
                </kbd>
              </TooltipContent>
            </Tooltip>
          ))}
        </div>

        {/* Divider */}
        <div className="h-px bg-border mx-2" />

        {/* Bottom Navigation */}
        <div className="flex flex-col gap-1 p-2">
          {bottomItems.map((item) => (
            <Tooltip key={item.id}>
              <TooltipTrigger asChild>
                <Button
                  variant={activeTab === item.id ? "secondary" : "ghost"}
                  size="sm"
                  onClick={() => onTabChange(item.id)}
                  className={cn(
                    "w-full h-10 justify-start p-2",
                    activeTab === item.id && "bg-secondary"
                  )}
                >
                  <item.icon className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent side="right" className="flex items-center gap-2">
                <span>{item.label}</span>
                <kbd className="text-xs bg-muted px-1 py-0.5 rounded">
                  {item.shortcut}
                </kbd>
              </TooltipContent>
            </Tooltip>
          ))}
        </div>
      </div>
    </TooltipProvider>
  );
}