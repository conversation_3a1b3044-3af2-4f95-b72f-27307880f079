'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Play, 
  Pause, 
  Volume2, 
  VolumeX,
  Download,
  Share,
  AudioWaveform,
  Headphones,
  MoreVertical,
  Copy,
  Trash2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';

interface AudioPreviewPanelProps {
  audioUrl?: string;
  isPlaying?: boolean;
  volume?: number;
  isMuted?: boolean;
  onPlayPause?: () => void;
  onVolumeChange?: (volume: number) => void;
  onMuteToggle?: () => void;
  onExport?: () => void;
  onShare?: () => void;
  className?: string;
}

export function AudioPreviewPanel({
  audioUrl,
  isPlaying = false,
  volume = 1,
  isMuted = false,
  onPlayPause,
  onVolumeChange,
  onMuteToggle,
  onExport,
  onShare,
  className
}: AudioPreviewPanelProps) {
  const [showWaveform, setShowWaveform] = useState(true);

  const handleVolumeChange = (value: number[]) => {
    onVolumeChange?.(value[0]);
  };

  return (
    <div className={cn("h-full flex flex-col", className)}>
      <Card className="flex-1 flex flex-col">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center gap-2">
              <Headphones className="h-4 w-4" />
              Audio Preview
            </CardTitle>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                  <MoreVertical className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem onClick={() => setShowWaveform(!showWaveform)}>
                  <AudioWaveform className="h-4 w-4 mr-2" />
                  {showWaveform ? 'Hide' : 'Show'} Waveform
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={onExport}>
                  <Download className="h-4 w-4 mr-2" />
                  Export Audio
                </DropdownMenuItem>
                <DropdownMenuItem onClick={onShare}>
                  <Share className="h-4 w-4 mr-2" />
                  Share Audio
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <Copy className="h-4 w-4 mr-2" />
                  Copy URL
                </DropdownMenuItem>
                <DropdownMenuItem className="text-destructive">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardHeader>
        
        <CardContent className="flex-1 flex flex-col gap-4">
          {/* Audio Status */}
          <div className="flex items-center gap-2">
            {audioUrl ? (
              <Badge variant="secondary" className="text-xs">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-1" />
                Ready
              </Badge>
            ) : (
              <Badge variant="outline" className="text-xs">
                <div className="w-2 h-2 bg-gray-400 rounded-full mr-1" />
                No Audio
              </Badge>
            )}
            {isPlaying && (
              <Badge variant="outline" className="text-xs">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-1 animate-pulse" />
                Playing
              </Badge>
            )}
          </div>

          {/* Waveform Visualization */}
          {showWaveform && (
            <div className="flex-1 bg-muted/20 rounded-lg p-4 flex items-center justify-center">
              {audioUrl ? (
                <div className="w-full h-24 flex items-end justify-center gap-1">
                  {/* Animated waveform bars */}
                  {Array.from({ length: 40 }, (_, i) => (
                    <div
                      key={i}
                      className={cn(
                        "bg-primary/60 rounded-full transition-all duration-300",
                        isPlaying ? "animate-pulse" : ""
                      )}
                      style={{
                        width: '2px',
                        height: `${Math.random() * 80 + 20}%`,
                        animationDelay: `${i * 50}ms`
                      }}
                    />
                  ))}
                </div>
              ) : (
                <div className="text-center text-muted-foreground">
                  <AudioWaveform className="h-8 w-8 mx-auto mb-2" />
                  <p className="text-sm">No audio loaded</p>
                </div>
              )}
            </div>
          )}

          {/* Playback Controls */}
          <div className="space-y-4">
            <div className="flex items-center justify-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={onPlayPause}
                disabled={!audioUrl}
                className="h-8 w-8 p-0"
              >
                {isPlaying ? (
                  <Pause className="h-4 w-4" />
                ) : (
                  <Play className="h-4 w-4" />
                )}
              </Button>
            </div>

            {/* Volume Control */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onMuteToggle}
                    className="h-6 w-6 p-0"
                  >
                    {isMuted ? (
                      <VolumeX className="h-3 w-3" />
                    ) : (
                      <Volume2 className="h-3 w-3" />
                    )}
                  </Button>
                  <span className="text-xs text-muted-foreground">Volume</span>
                </div>
                <span className="text-xs text-muted-foreground">
                  {Math.round(volume * 100)}%
                </span>
              </div>
              <Slider
                value={[volume]}
                onValueChange={handleVolumeChange}
                max={1}
                step={0.1}
                className="w-full"
              />
            </div>
          </div>

          <Separator />

          {/* Export Controls */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={onExport}
              disabled={!audioUrl}
              className="flex-1"
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={onShare}
              disabled={!audioUrl}
              className="flex-1"
            >
              <Share className="h-4 w-4 mr-2" />
              Share
            </Button>
          </div>

          {/* Audio Info */}
          {audioUrl && (
            <div className="space-y-2 text-xs text-muted-foreground">
              <div className="flex justify-between">
                <span>Format:</span>
                <span>MP3</span>
              </div>
              <div className="flex justify-between">
                <span>Quality:</span>
                <span>44.1kHz, 128kbps</span>
              </div>
              <div className="flex justify-between">
                <span>Size:</span>
                <span>~2.1 MB</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}