'use client';

import React, { useState, useCallback, useEffect, useRef } from 'react';
import { 
  ResizableHandle, 
  ResizablePanel, 
  ResizablePanelGroup 
} from '@/components/ui/resizable';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Play, 
  Pause, 
  SkipBack, 
  SkipForward,
  Volume2,
  VolumeX,
  Maximize2,
  Settings,
  PanelLeftClose,
  PanelLeftOpen,
  PanelRightClose,
  PanelRightOpen,
  Mic,
  Type,
  AudioWaveform,
  Download,
  Share,
  Bot,
  Zap
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useElevenLabs } from '@/hooks/use-elevenlabs';
import { useConversationalAI } from '@/hooks/use-conversational-ai';
import { toast } from 'sonner';

// Import components
import { AudioEditorSidebar } from './audio-editor-sidebar';
import { AudioPreviewPanel } from './audio-preview-panel';
import { TextEditorPanel } from './text-editor-panel';
import { VoiceSettingsPanel } from './voice-settings-panel';
import { AudioTimeline } from './audio-timeline';
import { VSCodeTabs, TabContent, TabPanel } from './vscode-tabs';
import { ConversationalAIPanel } from './conversational-ai-panel';
import { AudioLibraryPanel } from './audio-library-panel';
import { ProjectSettingsPanel } from './project-settings-panel';
import type { 
  ElevenLabsVoice, 
  GenerationState,
  ConversationalAIAgent 
} from '@/lib/elevenlabs/types';

interface ElevenLabsStudioLayoutProps {
  onAudioGenerated?: (audioUrl: string) => void;
  onProjectSaved?: (projectId: string) => void;
  className?: string;
}

export interface AudioProject {
  id: string;
  name: string;
  description?: string;
  voice: ElevenLabsVoice | null;
  text: string;
  settings: {
    stability: number;
    similarity_boost: number;
    style: number;
    use_speaker_boost: boolean;
  };
  audioUrl?: string;
  createdAt: number;
  updatedAt: number;
}

export interface AudioClip {
  id: string;
  text: string;
  audioUrl: string;
  duration: number;
  position: number;
  voice: ElevenLabsVoice;
  settings: any;
}

const DEFAULT_TABS = [
  { id: 'text-editor', title: 'Text Editor', modified: false },
  { id: 'voice-settings', title: 'Voice Settings', modified: false },
  { id: 'conversational-ai', title: 'Conversational AI', modified: false },
  { id: 'audio-library', title: 'Audio Library', modified: false },
];

export function ElevenLabsStudioLayout({
  onAudioGenerated,
  onProjectSaved,
  className = ''
}: ElevenLabsStudioLayoutProps) {
  // Layout state
  const [leftPanelCollapsed, setLeftPanelCollapsed] = useState(false);
  const [rightPanelCollapsed, setRightPanelCollapsed] = useState(false);
  const [bottomPanelCollapsed, setBottomPanelCollapsed] = useState(false);
  const [activeTab, setActiveTab] = useState('text-editor');
  const [tabs, setTabs] = useState(DEFAULT_TABS);

  // Audio state
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  
  // ElevenLabs integration
  const {
    voices,
    selectedVoice,
    generation,
    generateSpeech,
    loadVoices,
    selectVoice,
    isConfigured
  } = useElevenLabs();

  // Conversational AI integration
  const {
    agents,
    selectedAgent,
    isConnected,
    connect,
    disconnect,
    sendMessage
  } = useConversationalAI({
    apiKey: '', // Will be handled by the main store
    autoLoadAgents: true
  });
  
  // Project state
  const [currentProject, setCurrentProject] = useState<AudioProject | null>(null);
  const [audioClips, setAudioClips] = useState<AudioClip[]>([]);
  const [selectedClipId, setSelectedClipId] = useState<string | undefined>();
  const [currentAudioUrl, setCurrentAudioUrl] = useState<string | undefined>();

  // Refs
  const audioRef = useRef<HTMLAudioElement>(null);

  // Initialize
  useEffect(() => {
    if (isConfigured && voices.length === 0) {
      loadVoices();
    }
  }, [isConfigured, voices.length, loadVoices]);

  // Handle audio generation
  useEffect(() => {
    if (generation.audioUrl && generation.audioUrl !== currentAudioUrl) {
      setCurrentAudioUrl(generation.audioUrl);
      onAudioGenerated?.(generation.audioUrl);
    }
  }, [generation.audioUrl, currentAudioUrl, onAudioGenerated]);

  // Handlers
  const handleGenerateAudio = useCallback(async (
    text: string,
    voice: ElevenLabsVoice,
    settings: any
  ) => {
    if (!text.trim()) {
      toast.error('Please enter some text to generate audio');
      return;
    }

    try {
      await generateSpeech({
        text,
        voice_id: voice.voice_id,
        voice_settings: settings,
        model_id: 'eleven_multilingual_v2'
      });
      toast.success('Audio generated successfully!');
    } catch (error) {
      console.error('Audio generation error:', error);
      toast.error('Failed to generate audio');
    }
  }, [generateSpeech]);

  const handlePlayPause = useCallback(() => {
    if (audioRef.current) {
      if (isPlaying) {
        audioRef.current.pause();
      } else {
        audioRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  }, [isPlaying]);

  const handleTimeChange = useCallback((time: number) => {
    setCurrentTime(time);
    if (audioRef.current) {
      audioRef.current.currentTime = time;
    }
  }, []);

  const handleVolumeChange = useCallback((newVolume: number) => {
    setVolume(newVolume);
    if (audioRef.current) {
      audioRef.current.volume = newVolume;
    }
  }, []);

  const handleMuteToggle = useCallback(() => {
    setIsMuted(!isMuted);
    if (audioRef.current) {
      audioRef.current.muted = !isMuted;
    }
  }, [isMuted]);

  const handleClipSelect = useCallback((clip: AudioClip) => {
    setSelectedClipId(clip.id);
    setCurrentAudioUrl(clip.audioUrl);
  }, []);

  const handleClipDelete = useCallback((clipId: string) => {
    setAudioClips(prev => prev.filter(clip => clip.id !== clipId));
    if (selectedClipId === clipId) {
      setSelectedClipId(undefined);
    }
    toast.success('Clip deleted');
  }, [selectedClipId]);

  const handleClipDuplicate = useCallback((clip: AudioClip) => {
    const newClip: AudioClip = {
      ...clip,
      id: `${clip.id}-copy-${Date.now()}`,
      position: clip.position + clip.duration
    };
    setAudioClips(prev => [...prev, newClip]);
    toast.success('Clip duplicated');
  }, []);

  const handleExport = useCallback(async () => {
    if (!currentAudioUrl) {
      toast.error('No audio to export');
      return;
    }

    try {
      // Create download link
      const link = document.createElement('a');
      link.href = currentAudioUrl;
      link.download = `elevenlabs-audio-${Date.now()}.mp3`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      toast.success('Audio exported successfully!');
    } catch (error) {
      console.error('Export error:', error);
      toast.error('Failed to export audio');
    }
  }, [currentAudioUrl]);

  const handleShare = useCallback(() => {
    if (!currentAudioUrl) {
      toast.error('No audio to share');
      return;
    }

    if (navigator.share) {
      navigator.share({
        title: 'Generated Audio',
        text: 'Check out this audio generated with ElevenLabs',
        url: currentAudioUrl
      });
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(currentAudioUrl);
      toast.success('Audio URL copied to clipboard');
    }
  }, [currentAudioUrl]);

  const handleProjectSave = useCallback(() => {
    if (!currentProject) return;

    const updatedProject = {
      ...currentProject,
      updatedAt: Date.now()
    };

    setCurrentProject(updatedProject);
    onProjectSaved?.(updatedProject.id);
    toast.success('Project saved');
  }, [currentProject, onProjectSaved]);

  const handleTabClose = useCallback((tabId: string) => {
    setTabs(prev => prev.filter(tab => tab.id !== tabId));
    if (activeTab === tabId) {
      const remainingTabs = tabs.filter(tab => tab.id !== tabId);
      if (remainingTabs.length > 0) {
        setActiveTab(remainingTabs[0].id);
      }
    }
  }, [activeTab, tabs]);

  const handleNewTab = useCallback(() => {
    const newTab = {
      id: `new-tab-${Date.now()}`,
      title: 'Untitled',
      modified: true
    };
    setTabs(prev => [...prev, newTab]);
    setActiveTab(newTab.id);
  }, []);

  return (
    <div className={cn("h-screen bg-background flex flex-col", className)}>
      {/* Top Menu Bar */}
      <div className="h-8 bg-muted/30 border-b border-border flex items-center justify-between px-4">
        <div className="flex items-center space-x-4">
          <span className="text-xs font-medium">ElevenLabs Studio</span>
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setLeftPanelCollapsed(!leftPanelCollapsed)}
              className="h-6 w-6 p-0"
            >
              {leftPanelCollapsed ? <PanelLeftOpen className="h-3 w-3" /> : <PanelLeftClose className="h-3 w-3" />}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setRightPanelCollapsed(!rightPanelCollapsed)}
              className="h-6 w-6 p-0"
            >
              {rightPanelCollapsed ? <PanelRightOpen className="h-3 w-3" /> : <PanelRightClose className="h-3 w-3" />}
            </Button>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {generation.isGenerating && (
            <Badge variant="secondary" className="text-xs">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-1 animate-pulse" />
              Generating {Math.round(generation.progress)}%
            </Badge>
          )}
          {isConnected && (
            <Badge variant="outline" className="text-xs">
              <Bot className="h-3 w-3 mr-1" />
              AI Connected
            </Badge>
          )}
          <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
            <Settings className="h-3 w-3" />
          </Button>
        </div>
      </div>

      {/* Main Layout */}
      <div className="flex-1 overflow-hidden">
        <ResizablePanelGroup direction="horizontal">
          {/* Left Sidebar */}
          {!leftPanelCollapsed && (
            <>
              <ResizablePanel defaultSize={4} minSize={4} maxSize={8}>
                <AudioEditorSidebar 
                  activeTab={activeTab}
                  onTabChange={setActiveTab}
                />
              </ResizablePanel>
              <ResizableHandle />
            </>
          )}

          {/* Main Content Area */}
          <ResizablePanel defaultSize={leftPanelCollapsed ? 80 : 76} minSize={50}>
            <ResizablePanelGroup direction="vertical">
              {/* Top Section */}
              <ResizablePanel defaultSize={70} minSize={40}>
                <ResizablePanelGroup direction="horizontal">
                  {/* Main Editor */}
                  <ResizablePanel defaultSize={rightPanelCollapsed ? 100 : 70} minSize={50}>
                    <div className="h-full flex flex-col">
                      {/* Tab Bar */}
                      <VSCodeTabs
                        tabs={tabs}
                        activeTabId={activeTab}
                        onTabChange={setActiveTab}
                        onTabClose={handleTabClose}
                        onNewTab={handleNewTab}
                      />
                      
                      {/* Tab Content */}
                      <TabContent activeTabId={activeTab} className="flex-1">
                        <TabPanel tabId="text-editor" activeTabId={activeTab}>
                          <TextEditorPanel
                            onGenerate={handleGenerateAudio}
                            selectedVoice={selectedVoice}
                            generation={generation}
                            className="h-full"
                          />
                        </TabPanel>
                        
                        <TabPanel tabId="voice-settings" activeTabId={activeTab}>
                          <VoiceSettingsPanel
                            voices={voices}
                            selectedVoice={selectedVoice}
                            onVoiceSelect={selectVoice}
                            className="h-full"
                          />
                        </TabPanel>
                        
                        <TabPanel tabId="conversational-ai" activeTabId={activeTab}>
                          <ConversationalAIPanel
                            agents={agents}
                            selectedAgent={selectedAgent}
                            isConnected={isConnected}
                            onConnect={connect}
                            onDisconnect={disconnect}
                            onSendMessage={sendMessage}
                            className="h-full"
                          />
                        </TabPanel>
                        
                        <TabPanel tabId="audio-library" activeTabId={activeTab}>
                          <AudioLibraryPanel
                            clips={audioClips}
                            selectedClipId={selectedClipId}
                            onClipSelect={handleClipSelect}
                            onClipDelete={handleClipDelete}
                            onClipDuplicate={handleClipDuplicate}
                            className="h-full"
                          />
                        </TabPanel>
                      </TabContent>
                    </div>
                  </ResizablePanel>

                  {/* Right Panel */}
                  {!rightPanelCollapsed && (
                    <>
                      <ResizableHandle />
                      <ResizablePanel defaultSize={30} minSize={20} maxSize={40}>
                        <AudioPreviewPanel
                          audioUrl={currentAudioUrl}
                          isPlaying={isPlaying}
                          volume={volume}
                          isMuted={isMuted}
                          onPlayPause={handlePlayPause}
                          onVolumeChange={handleVolumeChange}
                          onMuteToggle={handleMuteToggle}
                          onExport={handleExport}
                          onShare={handleShare}
                          className="h-full"
                        />
                      </ResizablePanel>
                    </>
                  )}
                </ResizablePanelGroup>
              </ResizablePanel>

              {/* Bottom Timeline */}
              {!bottomPanelCollapsed && (
                <>
                  <ResizableHandle />
                  <ResizablePanel defaultSize={30} minSize={20} maxSize={50}>
                    <AudioTimeline
                      clips={audioClips}
                      currentTime={currentTime}
                      duration={duration}
                      isPlaying={isPlaying}
                      onTimeChange={handleTimeChange}
                      onPlayPause={handlePlayPause}
                      onClipSelect={handleClipSelect}
                      onClipDelete={handleClipDelete}
                      onClipDuplicate={handleClipDuplicate}
                      className="h-full"
                    />
                  </ResizablePanel>
                </>
              )}
            </ResizablePanelGroup>
          </ResizablePanel>
        </ResizablePanelGroup>
      </div>

      {/* Hidden audio element for playback */}
      <audio
        ref={audioRef}
        src={currentAudioUrl}
        onTimeUpdate={(e) => setCurrentTime(e.currentTarget.currentTime)}
        onLoadedMetadata={(e) => setDuration(e.currentTarget.duration)}
        onEnded={() => setIsPlaying(false)}
        preload="metadata"
        className="hidden"
      />
    </div>
  );
}