'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Play, 
  Pause, 
  Download, 
  Volume2, 
  Loader2, 
  AlertCircle, 
  CheckCircle,
  Settings,
  Mic,
  Type,
  AudioWaveform
} from 'lucide-react';
import { useTextToSpeech } from '@/hooks/use-elevenlabs';
import { useUserManagement } from '@/hooks/use-elevenlabs';
import { VoiceSelector } from './voice-selector';
import { VoiceSettings } from './voice-settings';
import { AudioPlayer } from './audio-player';
// import { QuotaIndicator } from './quota-indicator';
import { ELEVENLABS_MODELS, ELEVENLABS_OUTPUT_FORMATS } from '@/lib/elevenlabs/constants';
import { cleanTextForTTS, splitTextIntoChunks } from '@/lib/elevenlabs/utils';
import type { ElevenLabsVoice, ElevenLabsModel } from '@/lib/elevenlabs/types';

interface TextToSpeechProps {
  className?: string;
  defaultText?: string;
  onAudioGenerated?: (audioUrl: string) => void;
}

export function TextToSpeech({
  className = '',
  defaultText = '',
  onAudioGenerated: _onAudioGenerated
}: TextToSpeechProps) {
  const { generation, synthesize, play, pause, download, clearError } = useTextToSpeech();
  const { estimateCost, checkQuota } = useUserManagement();

  const [text, setText] = useState(defaultText);
  const [selectedModel, setSelectedModel] = useState(ELEVENLABS_MODELS.ELEVEN_MULTILINGUAL_V2);
  const [outputFormat, setOutputFormat] = useState(ELEVENLABS_OUTPUT_FORMATS.MP3_44100_128);
  const [voiceSettings, setVoiceSettings] = useState({
    stability: 0.5,
    similarity_boost: 0.5,
    style: 0.0,
    use_speaker_boost: true,
  });
  const [showSettings, setShowSettings] = useState(false);
  const [selectedVoice, setSelectedVoice] = useState<ElevenLabsVoice | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [costEstimate, setCostEstimate] = useState<any>(null);
  const [quotaCheck, setQuotaCheck] = useState<any>(null);

  // Calculate cost estimate when text changes
  useEffect(() => {
    if (text.length > 0) {
      estimateCost(text).then(setCostEstimate);
      checkQuota(text.length).then(setQuotaCheck);
    }
  }, [text, estimateCost, checkQuota]);

  // Clean up audio URL when component unmounts
  useEffect(() => {
    return () => {
      if (generation.audioUrl) {
        URL.revokeObjectURL(generation.audioUrl);
      }
    };
  }, [generation.audioUrl]);

  const handleSynthesize = async () => {
    if (!text.trim() || !selectedVoice) return;

    clearError();
    
    const cleanedText = cleanTextForTTS(text);
    const chunks = splitTextIntoChunks(cleanedText, 2500);
    
    if (chunks.length > 1) {
      // Handle long text by splitting into chunks
      // For now, just use the first chunk
      await synthesize(chunks[0], {
        voice: selectedVoice,
        model: { model_id: selectedModel } as ElevenLabsModel,
        settings: voiceSettings,
      });
    } else {
      await synthesize(cleanedText, {
        voice: selectedVoice,
        model: { model_id: selectedModel } as ElevenLabsModel,
        settings: voiceSettings,
      });
    }
  };

  const handlePlay = () => {
    if (generation.audioUrl) {
      play();
      setIsPlaying(true);
    }
  };

  const handlePause = () => {
    pause();
    setIsPlaying(false);
  };

  // const handleStop = () => {
  //   stop();
  //   setIsPlaying(false);
  // };

  const handleDownload = () => {
    if (generation.generatedAudio) {
      const filename = `speech_${selectedVoice?.name || 'voice'}_${Date.now()}.mp3`;
      download(filename);
    }
  };

  const canGenerate = Boolean(
    text.trim() && 
    selectedVoice && 
    !generation.isGenerating &&
    quotaCheck?.has_quota !== false
  );

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Type className="h-5 w-5" />
            Text-to-Speech
          </CardTitle>
          <CardDescription>
            Convert text to natural-sounding speech using ElevenLabs AI voices
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Quota Indicator */}
      {/* <QuotaIndicator /> */}

      {/* Voice Selection */}
      <VoiceSelector
        onVoiceSelect={setSelectedVoice}
        selectedVoice={selectedVoice}
        compact={false}
      />

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Text Input */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mic className="h-5 w-5" />
              Text Input
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="text-input">Text to synthesize</Label>
              <Textarea
                id="text-input"
                value={text}
                onChange={(e) => setText(e.target.value)}
                placeholder="Enter the text you want to convert to speech..."
                className="min-h-[120px] resize-none"
                maxLength={5000}
              />
              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span>{text.length} / 5000 characters</span>
                {costEstimate && (
                  <span>
                    Est. cost: ${costEstimate.estimated_cost.toFixed(4)}
                  </span>
                )}
              </div>
            </div>

            {/* Model Selection */}
            <div className="space-y-2">
              <Label>Model</Label>
              <Select value={selectedModel} onValueChange={(value) => setSelectedModel(value as any)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={ELEVENLABS_MODELS.ELEVEN_MULTILINGUAL_V2}>
                    Eleven Multilingual v2
                  </SelectItem>
                  <SelectItem value={ELEVENLABS_MODELS.ELEVEN_TURBO_V2_5}>
                    Eleven Turbo v2.5
                  </SelectItem>
                  <SelectItem value={ELEVENLABS_MODELS.ELEVEN_FLASH_V2_5}>
                    Eleven Flash v2.5
                  </SelectItem>
                  <SelectItem value={ELEVENLABS_MODELS.ELEVEN_V3_ALPHA}>
                    Eleven v3 (Alpha)
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Output Format */}
            <div className="space-y-2">
              <Label>Output Format</Label>
              <Select value={outputFormat} onValueChange={(value) => setOutputFormat(value as any)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value={ELEVENLABS_OUTPUT_FORMATS.MP3_44100_128}>
                    MP3 44.1kHz 128kbps
                  </SelectItem>
                  <SelectItem value={ELEVENLABS_OUTPUT_FORMATS.MP3_44100_192}>
                    MP3 44.1kHz 192kbps
                  </SelectItem>
                  <SelectItem value={ELEVENLABS_OUTPUT_FORMATS.PCM_44100}>
                    PCM 44.1kHz
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Voice Settings Toggle */}
            <Button
              variant="outline"
              onClick={() => setShowSettings(!showSettings)}
              className="w-full"
            >
              <Settings className="h-4 w-4 mr-2" />
              {showSettings ? 'Hide' : 'Show'} Voice Settings
            </Button>

            {/* Voice Settings */}
            {showSettings && (
              <VoiceSettings
                settings={voiceSettings}
                onChange={setVoiceSettings}
                voice={selectedVoice}
              />
            )}

            {/* Generate Button */}
            <Button
              onClick={handleSynthesize}
              disabled={!canGenerate}
              className="w-full"
              size="lg"
            >
              {generation.isGenerating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Volume2 className="h-4 w-4 mr-2" />
                  Generate Speech
                </>
              )}
            </Button>

            {/* Quota Warning */}
            {quotaCheck && !quotaCheck.has_quota && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Insufficient quota. You need {text.length} characters but only have {quotaCheck.available} remaining.
                </AlertDescription>
              </Alert>
            )}

            {/* Error Display */}
            {generation.error && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{generation.error}</AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* Audio Output */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AudioWaveform className="h-5 w-5" />
              Audio Output
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Progress */}
            {generation.isGenerating && (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label>Generation Progress</Label>
                  <span className="text-sm text-muted-foreground">
                    {Math.round(generation.progress)}%
                  </span>
                </div>
                <Progress value={generation.progress} className="h-2" />
              </div>
            )}

            {/* Audio Player */}
            {generation.audioUrl && (
              <AudioPlayer
                src={generation.audioUrl}
                onPlay={() => setIsPlaying(true)}
                onPause={() => setIsPlaying(false)}
                onEnded={() => setIsPlaying(false)}
                className="w-full"
              />
            )}

            {/* Success State */}
            {generation.audioUrl && !generation.isGenerating && (
              <Alert>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  Audio generated successfully! You can play it above or download it.
                </AlertDescription>
              </Alert>
            )}

            {/* Audio Controls */}
            {generation.audioUrl && (
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={isPlaying ? handlePause : handlePlay}
                  disabled={generation.isGenerating}
                >
                  {isPlaying ? (
                    <>
                      <Pause className="h-4 w-4 mr-2" />
                      Pause
                    </>
                  ) : (
                    <>
                      <Play className="h-4 w-4 mr-2" />
                      Play
                    </>
                  )}
                </Button>
                <Button
                  variant="outline"
                  onClick={handleDownload}
                  disabled={generation.isGenerating}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
              </div>
            )}

            {/* Audio Info */}
            {generation.audioUrl && (
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <Label className="text-xs text-muted-foreground">Voice</Label>
                  <p className="font-medium">{selectedVoice?.name}</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">Model</Label>
                  <p className="font-medium">{selectedModel}</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">Characters</Label>
                  <p className="font-medium">{text.length}</p>
                </div>
                <div>
                  <Label className="text-xs text-muted-foreground">Format</Label>
                  <p className="font-medium">{outputFormat}</p>
                </div>
              </div>
            )}

            {/* Placeholder */}
            {!generation.audioUrl && !generation.isGenerating && (
              <div className="text-center py-12 text-muted-foreground">
                <Volume2 className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Generated audio will appear here</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}