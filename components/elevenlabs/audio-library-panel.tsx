'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  AudioWaveform, 
  Search, 
  Play, 
  Pause,
  Download,
  Copy,
  Trash2,
  MoreVertical,
  Filter,
  Grid,
  List,
  Upload
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatDuration } from '@/lib/elevenlabs/utils';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';
import type { AudioClip } from './elevenlabs-studio-layout';

interface AudioLibraryPanelProps {
  clips: AudioClip[];
  selectedClipId?: string;
  onClipSelect: (clip: AudioClip) => void;
  onClipDelete: (clipId: string) => void;
  onClipDuplicate: (clip: AudioClip) => void;
  className?: string;
}

export function AudioLibraryPanel({
  clips,
  selectedClipId,
  onClipSelect,
  onClipDelete,
  onClipDuplicate,
  className
}: AudioLibraryPanelProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  const [playingClipId, setPlayingClipId] = useState<string | null>(null);
  const [filterBy, setFilterBy] = useState<string>('all');

  const filteredClips = clips.filter(clip => {
    const matchesSearch = clip.text.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         clip.voice.name.toLowerCase().includes(searchQuery.toLowerCase());
    
    if (filterBy === 'all') return matchesSearch;
    if (filterBy === 'recent') return matchesSearch; // Add recent logic
    if (filterBy === 'voice') return matchesSearch; // Add voice filter logic
    
    return matchesSearch;
  });

  const handlePlay = (clip: AudioClip) => {
    if (playingClipId === clip.id) {
      setPlayingClipId(null);
    } else {
      setPlayingClipId(clip.id);
      onClipSelect(clip);
    }
  };

  const handleDownload = (clip: AudioClip) => {
    const link = document.createElement('a');
    link.href = clip.audioUrl;
    link.download = `${clip.voice.name}-${Date.now()}.mp3`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const renderClipCard = (clip: AudioClip) => {
    const isSelected = selectedClipId === clip.id;
    const isPlaying = playingClipId === clip.id;

    if (viewMode === 'grid') {
      return (
        <div
          key={clip.id}
          className={cn(
            "p-3 border rounded-lg cursor-pointer transition-all hover:border-primary/50",
            isSelected && "border-primary bg-primary/5"
          )}
          onClick={() => onClipSelect(clip)}
        >
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <AudioWaveform className="h-4 w-4 text-primary" />
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                    <MoreVertical className="h-3 w-3" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => handlePlay(clip)}>
                    {isPlaying ? <Pause className="h-4 w-4 mr-2" /> : <Play className="h-4 w-4 mr-2" />}
                    {isPlaying ? 'Pause' : 'Play'}
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleDownload(clip)}>
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => onClipDuplicate(clip)}>
                    <Copy className="h-4 w-4 mr-2" />
                    Duplicate
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    onClick={() => onClipDelete(clip.id)}
                    className="text-destructive"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            
            <div className="space-y-1">
              <p className="text-xs line-clamp-2">{clip.text}</p>
              <div className="flex items-center justify-between">
                <Badge variant="outline" className="text-xs">
                  {clip.voice.name}
                </Badge>
                <span className="text-xs text-muted-foreground">
                  {formatDuration(clip.duration)}
                </span>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div
        key={clip.id}
        className={cn(
          "flex items-center gap-3 p-3 border rounded-lg cursor-pointer transition-all hover:border-primary/50",
          isSelected && "border-primary bg-primary/5"
        )}
        onClick={() => onClipSelect(clip)}
      >
        <div className="shrink-0">
          <Button
            variant="ghost"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              handlePlay(clip);
            }}
            className="h-8 w-8 p-0"
          >
            {isPlaying ? (
              <Pause className="h-4 w-4" />
            ) : (
              <Play className="h-4 w-4" />
            )}
          </Button>
        </div>
        
        <div className="flex-1 min-w-0">
          <p className="text-sm truncate">{clip.text}</p>
          <div className="flex items-center gap-2 mt-1">
            <Badge variant="outline" className="text-xs">
              {clip.voice.name}
            </Badge>
            <span className="text-xs text-muted-foreground">
              {formatDuration(clip.duration)}
            </span>
          </div>
        </div>
        
        <div className="shrink-0">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-6 w-6 p-0">
                <MoreVertical className="h-3 w-3" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => handleDownload(clip)}>
                <Download className="h-4 w-4 mr-2" />
                Download
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => onClipDuplicate(clip)}>
                <Copy className="h-4 w-4 mr-2" />
                Duplicate
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                onClick={() => onClipDelete(clip.id)}
                className="text-destructive"
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    );
  };

  return (
    <div className={cn("h-full flex flex-col", className)}>
      <Card className="flex-1 flex flex-col">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center gap-2">
              <AudioWaveform className="h-4 w-4" />
              Audio Library
            </CardTitle>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setViewMode(viewMode === 'grid' ? 'list' : 'grid')}
                className="h-6 w-6 p-0"
              >
                {viewMode === 'grid' ? <List className="h-3 w-3" /> : <Grid className="h-3 w-3" />}
              </Button>
              <Button variant="ghost" size="sm" className="h-6 text-xs">
                <Upload className="h-3 w-3 mr-1" />
                Import
              </Button>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="flex-1 flex flex-col gap-4">
          {/* Search and Filters */}
          <div className="space-y-2">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search clips..."
                className="pl-10 h-8"
              />
            </div>
            
            <div className="flex items-center gap-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-6 text-xs">
                    <Filter className="h-3 w-3 mr-1" />
                    Filter
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => setFilterBy('all')}>
                    All Clips
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setFilterBy('recent')}>
                    Recent
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => setFilterBy('voice')}>
                    By Voice
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              
              <Badge variant="outline" className="text-xs">
                {filteredClips.length} clips
              </Badge>
            </div>
          </div>

          {/* Clips List */}
          <ScrollArea className="flex-1">
            <div className={cn(
              "space-y-2",
              viewMode === 'grid' && "grid grid-cols-2 gap-2 space-y-0"
            )}>
              {filteredClips.length === 0 ? (
                <div className="text-center text-muted-foreground py-8">
                  <AudioWaveform className="h-8 w-8 mx-auto mb-2" />
                  {searchQuery ? (
                    <p className="text-sm">No clips found matching "{searchQuery}"</p>
                  ) : (
                    <>
                      <p className="text-sm mb-2">No audio clips yet</p>
                      <p className="text-xs">Generate some audio to see it here</p>
                    </>
                  )}
                </div>
              ) : (
                filteredClips.map(renderClipCard)
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
}